/**
 * GPU实例更新器
 * 使用WebGL计算着色器加速实例更新
 */
import * as THREE from 'three';
import { EnhancedInstanceData } from './EnhancedInstancedRenderingSystem';
import { Debug } from '../../utils/Debug';

/**
 * GPU实例更新器配置接口
 */
export interface GPUInstanceUpdaterOptions {
  /** 是否使用WebGL2 */
  useWebGL2?: boolean;
  /** 是否使用计算着色器 */
  useComputeShader?: boolean;
  /** 是否使用批处理 */
  useBatching?: boolean;
  /** 批处理大小 */
  batchSize?: number;
  /** 是否使用双缓冲 */
  useDoubleBuffering?: boolean;
  /** 是否使用并行更新 */
  useParallelUpdate?: boolean;
  /** 是否使用调试可视化 */
  useDebugVisualization?: boolean;
}

/**
 * GPU实例更新器类
 */
export class GPUInstanceUpdater {
  /** 是否使用WebGL2 */
  private useWebGL2: boolean;

  /** 是否使用计算着色器 */
  private useComputeShader: boolean;

  /** 是否使用批处理 */
  private useBatching: boolean;

  /** 批处理大小 */
  private batchSize: number;

  /** 是否使用双缓冲 */
  private useDoubleBuffering: boolean;

  /** 是否使用并行更新 */
  private useParallelUpdate: boolean;

  /** 是否使用调试可视化 */
  private useDebugVisualization: boolean;

  /** WebGL渲染器 */
  private renderer: THREE.WebGLRenderer | null = null;

  /** 计算场景 */
  private computeScene: THREE.Scene | null = null;

  /** 计算相机 */
  private computeCamera: THREE.Camera | null = null;

  /** 计算渲染目标 */
  private computeRenderTarget: THREE.WebGLRenderTarget | null = null;

  /** 计算着色器 */
  private computeShader: THREE.ShaderMaterial | null = null;

  /** 计算网格 */
  private computeMesh: THREE.Mesh | null = null;

  /** 实例矩阵纹理 */
  private instanceMatrixTexture: THREE.DataTexture | null = null;

  /** 实例矩阵纹理宽度 */
  private instanceMatrixTextureWidth: number = 0;

  /** 实例矩阵纹理高度 */
  private instanceMatrixTextureHeight: number = 0;

  /** 是否支持WebGL2 */
  private supportsWebGL2: boolean = false;

  /** 是否支持计算着色器 */
  private supportsComputeShader: boolean = false;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 工作线程 */
  private workers: Worker[] = [];

  /** 工作线程数量 */
  private workerCount: number = 0;

  /**
   * 创建GPU实例更新器
   * @param options GPU实例更新器配置
   */
  constructor(options: GPUInstanceUpdaterOptions = {}) {
    this.useWebGL2 = options.useWebGL2 !== undefined ? options.useWebGL2 : true;
    this.useComputeShader = options.useComputeShader !== undefined ? options.useComputeShader : true;
    this.useBatching = options.useBatching !== undefined ? options.useBatching : true;
    this.batchSize = options.batchSize || 1024;
    this.useDoubleBuffering = options.useDoubleBuffering !== undefined ? options.useDoubleBuffering : true;
    this.useParallelUpdate = options.useParallelUpdate !== undefined ? options.useParallelUpdate : true;
    this.useDebugVisualization = options.useDebugVisualization !== undefined ? options.useDebugVisualization : false;

    // 检查WebGL支持
    this.checkWebGLSupport();

    // 初始化工作线程
    if (this.useParallelUpdate) {
      this.initializeWorkers();
    }
  }

  /**
   * 检查WebGL支持
   */
  private checkWebGLSupport(): void {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl2');
      this.supportsWebGL2 = !!gl;
      this.supportsComputeShader = this.supportsWebGL2 && this.checkComputeShaderSupport(gl as WebGL2RenderingContext);

      if (!this.supportsWebGL2 && this.useWebGL2) {
        Debug.warn('GPUInstanceUpdater', 'WebGL2不支持，回退到WebGL1');
        this.useWebGL2 = false;
      }

      if (!this.supportsComputeShader && this.useComputeShader) {
        Debug.warn('GPUInstanceUpdater', '计算着色器不支持，回退到传统渲染');
        this.useComputeShader = false;
      }
    } catch (error) {
      this.supportsWebGL2 = false;
      this.supportsComputeShader = false;
      Debug.warn('GPUInstanceUpdater', 'WebGL2不支持，回退到WebGL1');
      this.useWebGL2 = false;
      this.useComputeShader = false;
    }
  }

  /**
   * 检查计算着色器支持
   * @param gl WebGL2渲染上下文
   * @returns 是否支持计算着色器
   */
  private checkComputeShaderSupport(gl: WebGL2RenderingContext): boolean {
    // 检查是否支持变换反馈
    return !!gl.getExtension('EXT_color_buffer_float');
  }

  /**
   * 初始化工作线程
   */
  private initializeWorkers(): void {
    // 获取CPU核心数
    const cpuCount = navigator.hardwareConcurrency || 4;
    this.workerCount = Math.max(1, Math.min(cpuCount - 1, 4));

    // 创建工作线程
    for (let i = 0; i < this.workerCount; i++) {
      // 在实际项目中，这里应该创建Web Worker
      // 这里使用简化的实现
    }
  }

  /**
   * 初始化GPU实例更新器
   * @param instanceCount 实例数量
   */
  private initialize(instanceCount: number): void {
    if (this.initialized) {
      return;
    }

    // 创建WebGL渲染器
    this.renderer = new THREE.WebGLRenderer({
      antialias: false,
      precision: 'highp',
      powerPreference: 'high-performance'
    });
    this.renderer.setSize(1, 1);

    // 创建计算场景和相机
    this.computeScene = new THREE.Scene();
    this.computeCamera = new THREE.OrthographicCamera(-1, 1, 1, -1, 0, 1);

    // 计算实例矩阵纹理尺寸
    this.calculateInstanceMatrixTextureSize(instanceCount);

    // 创建实例矩阵纹理
    this.createInstanceMatrixTexture(instanceCount);

    // 创建计算着色器
    this.createComputeShader();

    // 创建计算网格
    this.createComputeMesh();

    // 创建计算渲染目标
    this.createComputeRenderTarget();

    this.initialized = true;
  }

  /**
   * 计算实例矩阵纹理尺寸
   * @param instanceCount 实例数量
   */
  private calculateInstanceMatrixTextureSize(instanceCount: number): void {
    // 每个实例需要4个像素（4x4矩阵）
    const pixelCount = instanceCount * 4;

    // 计算纹理宽度和高度
    this.instanceMatrixTextureWidth = Math.ceil(Math.sqrt(pixelCount));
    this.instanceMatrixTextureHeight = Math.ceil(pixelCount / this.instanceMatrixTextureWidth);
  }

  /**
   * 创建实例矩阵纹理
   * @param instanceCount 实例数量
   */
  private createInstanceMatrixTexture(instanceCount: number): void {
    // 创建实例矩阵纹理
    const data = new Float32Array(this.instanceMatrixTextureWidth * this.instanceMatrixTextureHeight * 4);
    this.instanceMatrixTexture = new THREE.DataTexture(
      data,
      this.instanceMatrixTextureWidth,
      this.instanceMatrixTextureHeight,
      THREE.RGBAFormat,
      THREE.FloatType
    );
    this.instanceMatrixTexture.needsUpdate = true;
  }

  /**
   * 创建计算着色器
   */
  private createComputeShader(): void {
    // 创建计算着色器
    this.computeShader = new THREE.ShaderMaterial({
      vertexShader: `
        varying vec2 vUv;

        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        varying vec2 vUv;
        uniform sampler2D instanceMatrixTexture;
        uniform sampler2D instanceDataTexture;

        void main() {
          // 获取实例数据
          vec4 instanceData = texture2D(instanceDataTexture, vUv);

          // 计算实例矩阵
          mat4 instanceMatrix = mat4(
            vec4(1.0, 0.0, 0.0, 0.0),
            vec4(0.0, 1.0, 0.0, 0.0),
            vec4(0.0, 0.0, 1.0, 0.0),
            vec4(instanceData.xyz, 1.0)
          );

          // 输出实例矩阵
          gl_FragColor = instanceMatrix[int(mod(gl_FragCoord.x, 4.0))];
        }
      `,
      uniforms: {
        instanceMatrixTexture: { value: this.instanceMatrixTexture },
        instanceDataTexture: { value: null }
      }
    });
  }

  /**
   * 创建计算网格
   */
  private createComputeMesh(): void {
    // 创建计算网格
    const geometry = new THREE.PlaneBufferGeometry(2, 2);
    this.computeMesh = new THREE.Mesh(geometry, this.computeShader!);
    this.computeScene!.add(this.computeMesh);
  }

  /**
   * 创建计算渲染目标
   */
  private createComputeRenderTarget(): void {
    // 创建计算渲染目标
    this.computeRenderTarget = new THREE.WebGLRenderTarget(
      this.instanceMatrixTextureWidth,
      this.instanceMatrixTextureHeight,
      {
        format: THREE.RGBAFormat,
        type: THREE.FloatType,
        minFilter: THREE.NearestFilter,
        magFilter: THREE.NearestFilter,
        stencilBuffer: false,
        depthBuffer: false
      }
    );
  }

  /**
   * 使用GPU更新实例
   * @param instancedMesh 实例化网格
   * @param instances 实例数据
   */
  public updateWithGPU(instancedMesh: THREE.InstancedMesh, instances: EnhancedInstanceData[]): void {
    // 如果不支持WebGL2或计算着色器，则使用CPU更新
    if (!this.supportsWebGL2 || !this.supportsComputeShader) {
      this.updateWithCPU(instancedMesh, instances);
      return;
    }

    // 初始化GPU实例更新器
    if (!this.initialized) {
      this.initialize(instancedMesh.count);
    }

    // 如果使用批处理，则分批更新
    if (this.useBatching) {
      this.updateWithBatching(instancedMesh, instances);
    } else {
      // 否则一次性更新
      this.updateInstancesWithGPU(instancedMesh, instances);
    }
  }

  /**
   * 使用批处理更新实例
   * @param instancedMesh 实例化网格
   * @param instances 实例数据
   */
  private updateWithBatching(instancedMesh: THREE.InstancedMesh, instances: EnhancedInstanceData[]): void {
    // 计算批次数
    const batchCount = Math.ceil(instances.length / this.batchSize);

    // 分批更新
    for (let i = 0; i < batchCount; i++) {
      // 计算批次范围
      const start = i * this.batchSize;
      const end = Math.min(start + this.batchSize, instances.length);
      const batchInstances = instances.slice(start, end);

      // 更新批次
      this.updateInstancesWithGPU(instancedMesh, batchInstances, start);
    }
  }

  /**
   * 使用GPU更新实例
   * @param instancedMesh 实例化网格
   * @param instances 实例数据
   * @param startIndex 起始索引
   */
  private updateInstancesWithGPU(instancedMesh: THREE.InstancedMesh, instances: EnhancedInstanceData[], startIndex: number = 0): void {
    if (!this.renderer || !this.computeScene || !this.computeCamera || !this.computeRenderTarget || !this.computeShader) {
      return;
    }

    // 创建实例数据纹理
    const instanceDataTexture = this.createInstanceDataTexture(instances);

    // 设置计算着色器的实例数据纹理
    this.computeShader.uniforms.instanceDataTexture.value = instanceDataTexture;

    // 渲染计算着色器
    this.renderer.setRenderTarget(this.computeRenderTarget);
    this.renderer.render(this.computeScene, this.computeCamera);
    this.renderer.setRenderTarget(null);

    // 读取计算结果
    const pixelBuffer = new Float32Array(this.instanceMatrixTextureWidth * this.instanceMatrixTextureHeight * 4);
    this.renderer.readRenderTargetPixels(
      this.computeRenderTarget,
      0,
      0,
      this.instanceMatrixTextureWidth,
      this.instanceMatrixTextureHeight,
      pixelBuffer
    );

    // 更新实例矩阵
    this.updateInstanceMatrices(instancedMesh, pixelBuffer, instances.length, startIndex);

    // 释放资源
    (instanceDataTexture as any).dispose();
  }

  /**
   * 创建实例数据纹理
   * @param instances 实例数据
   * @returns 实例数据纹理
   */
  private createInstanceDataTexture(instances: EnhancedInstanceData[]): THREE.DataTexture {
    // 创建实例数据
    const data = new Float32Array(instances.length * 4);

    // 填充实例数据
    for (let i = 0; i < instances.length; i++) {
      const instance = instances[i];
      const offset = i * 4;

      // 位置
      data[offset] = instance.position.x;
      data[offset + 1] = instance.position.y;
      data[offset + 2] = instance.position.z;

      // 可见性
      data[offset + 3] = instance.visible !== false ? 1.0 : 0.0;
    }

    // 计算纹理尺寸
    const width = Math.ceil(Math.sqrt(instances.length));
    const height = Math.ceil(instances.length / width);

    // 创建纹理
    const texture = new THREE.DataTexture(
      data,
      width,
      height,
      THREE.RGBAFormat,
      THREE.FloatType
    );
    texture.needsUpdate = true;

    return texture;
  }

  /**
   * 更新实例矩阵
   * @param instancedMesh 实例化网格
   * @param pixelBuffer 像素缓冲区
   * @param instanceCount 实例数量
   * @param startIndex 起始索引
   */
  private updateInstanceMatrices(instancedMesh: THREE.InstancedMesh, pixelBuffer: Float32Array, instanceCount: number, startIndex: number): void {
    // 创建临时矩阵
    const matrix = new THREE.Matrix4();

    // 更新实例矩阵
    for (let i = 0; i < instanceCount; i++) {
      // 计算像素偏移
      const pixelOffset = i * 16; // 每个实例4x4矩阵，每个元素4字节

      // 设置矩阵元素
      matrix.set(
        pixelBuffer[pixelOffset], pixelBuffer[pixelOffset + 1], pixelBuffer[pixelOffset + 2], pixelBuffer[pixelOffset + 3],
        pixelBuffer[pixelOffset + 4], pixelBuffer[pixelOffset + 5], pixelBuffer[pixelOffset + 6], pixelBuffer[pixelOffset + 7],
        pixelBuffer[pixelOffset + 8], pixelBuffer[pixelOffset + 9], pixelBuffer[pixelOffset + 10], pixelBuffer[pixelOffset + 11],
        pixelBuffer[pixelOffset + 12], pixelBuffer[pixelOffset + 13], pixelBuffer[pixelOffset + 14], pixelBuffer[pixelOffset + 15]
      );

      // 设置实例矩阵
      instancedMesh.setMatrixAt(startIndex + i, matrix);
    }

    // 标记实例矩阵需要更新
    instancedMesh.instanceMatrix.needsUpdate = true;
  }

  /**
   * 使用CPU更新实例
   * @param instancedMesh 实例化网格
   * @param instances 实例数据
   */
  private updateWithCPU(instancedMesh: THREE.InstancedMesh, instances: EnhancedInstanceData[]): void {
    // 创建临时矩阵
    const matrix = new THREE.Matrix4();

    // 更新实例矩阵
    for (let i = 0; i < instances.length; i++) {
      // 获取实例数据
      const instance = instances[i];

      // 如果实例不可见，则跳过
      if (instance.visible === false) {
        continue;
      }

      // 计算实例矩阵
      matrix.compose(
        instance.position,
        instance.rotation,
        instance.scale
      );

      // 设置实例矩阵
      instancedMesh.setMatrixAt(i, matrix);
    }

    // 标记实例矩阵需要更新
    instancedMesh.instanceMatrix.needsUpdate = true;
  }

  /**
   * 使用并行更新实例
   * @param instancedMesh 实例化网格
   * @param instances 实例数据
   */
  private updateWithParallel(instancedMesh: THREE.InstancedMesh, instances: EnhancedInstanceData[]): void {
    // 如果没有工作线程，则使用CPU更新
    if (this.workerCount === 0) {
      this.updateWithCPU(instancedMesh, instances);
      return;
    }

    // 计算每个工作线程的实例数量
    const instancesPerWorker = Math.ceil(instances.length / this.workerCount);

    // 分配实例到工作线程
    for (let i = 0; i < this.workerCount; i++) {
      // 计算工作线程范围
      const start = i * instancesPerWorker;
      const end = Math.min(start + instancesPerWorker, instances.length);
      const _workerInstances = instances.slice(start, end);

      // 在实际项目中，这里应该发送消息给工作线程
      // 这里使用简化的实现
    }

    // 在实际项目中，这里应该等待工作线程完成
    // 这里使用简化的实现
  }

  /**
   * 销毁GPU实例更新器
   */
  public dispose(): void {
    // 销毁渲染器
    if (this.renderer) {
      (this.renderer as any).dispose();
      this.renderer = null;
    }

    // 销毁计算渲染目标
    if (this.computeRenderTarget) {
      (this.computeRenderTarget as any).dispose();
      this.computeRenderTarget = null;
    }

    // 销毁计算网格
    if (this.computeMesh) {
      (this.computeMesh.geometry as any).dispose();
      (this.computeMesh.material as THREE.Material).dispose();
      this.computeMesh = null;
    }

    // 销毁计算着色器
    if (this.computeShader) {
      (this.computeShader as any).dispose();
      this.computeShader = null;
    }

    // 销毁实例矩阵纹理
    if (this.instanceMatrixTexture) {
      (this.instanceMatrixTexture as any).dispose();
      this.instanceMatrixTexture = null;
    }

    // 销毁工作线程
    for (const worker of this.workers) {
      worker.terminate();
    }
    this.workers = [];

    this.initialized = false;
  }
}
