/**
 * 遮挡剔除系统
 * 用于剔除被其他物体遮挡的物体，提高渲染性能
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { Camera } from '../Camera';
import { Scene } from '../../scene/Scene';
import type { Transform } from '../../scene/Transform';
import { CullableComponent } from './CullableComponent';
import { Debug } from '../../utils/Debug';

/**
 * 遮挡剔除系统配置接口
 */
export interface OcclusionCullingSystemOptions {
  /** 是否启用 */
  enabled?: boolean;
  /** 遮挡剔除算法 */
  algorithm?: OcclusionCullingAlgorithm;
  /** 遮挡查询精度 */
  precision?: number;
  /** 是否使用层次Z缓冲区 */
  useHierarchicalZBuffer?: boolean;
  /** 是否使用遮挡查询 */
  useOcclusionQuery?: boolean;
  /** 是否使用GPU加速 */
  useGPU?: boolean;
  /** 是否使用保守遮挡剔除 */
  useConservativeOcclusion?: boolean;
  /** 是否使用时间一致性 */
  useTemporalCoherence?: boolean;
  /** 是否使用调试可视化 */
  useDebugVisualization?: boolean;
  /** 遮挡查询间隔（帧） */
  occlusionQueryInterval?: number;
  /** 遮挡查询超时（毫秒） */
  occlusionQueryTimeout?: number;
  /** 遮挡查询批处理大小 */
  occlusionQueryBatchSize?: number;
}

/**
 * 遮挡剔除算法枚举
 */
export enum OcclusionCullingAlgorithm {
  /** 层次Z缓冲区 */
  HIERARCHICAL_Z_BUFFER = 'hierarchical_z_buffer',
  /** 遮挡查询 */
  OCCLUSION_QUERY = 'occlusion_query',
  /** 软件光栅化 */
  SOFTWARE_RASTERIZATION = 'software_rasterization',
  /** 硬件遮挡查询 */
  HARDWARE_OCCLUSION_QUERY = 'hardware_occlusion_query'
}

/**
 * 遮挡剔除状态枚举
 */
export enum OcclusionCullingState {
  /** 可见 */
  VISIBLE = 'visible',
  /** 不可见 */
  INVISIBLE = 'invisible',
  /** 未知 */
  UNKNOWN = 'unknown',
  /** 查询中 */
  QUERYING = 'querying'
}

/**
 * 遮挡剔除结果接口
 */
export interface OcclusionCullingResult {
  /** 可见物体数量 */
  visibleCount: number;
  /** 不可见物体数量 */
  invisibleCount: number;
  /** 查询中物体数量 */
  queryingCount: number;
  /** 未知物体数量 */
  unknownCount: number;
  /** 总物体数量 */
  totalCount: number;
  /** 剔除率 */
  cullingRate: number;
  /** 查询时间（毫秒） */
  queryTime: number;
}

/**
 * 遮挡剔除系统类
 */
export class OcclusionCullingSystem extends System {
  /** 系统类型 */
  private static readonly TYPE: string = 'OcclusionCullingSystem';



  /** 遮挡剔除算法 */
  private algorithm: OcclusionCullingAlgorithm;

  /** 遮挡查询精度 */
  private _precision: number;

  /** 是否使用层次Z缓冲区 */
  private useHierarchicalZBuffer: boolean;

  /** 是否使用遮挡查询 */
  private useOcclusionQuery: boolean;

  /** 是否使用GPU加速 */
  private _useGPU: boolean;

  /** 是否使用保守遮挡剔除 */
  private _useConservativeOcclusion: boolean;

  /** 是否使用时间一致性 */
  private _useTemporalCoherence: boolean;

  /** 是否使用调试可视化 */
  private useDebugVisualization: boolean;

  /** 遮挡查询间隔（帧） */
  private occlusionQueryInterval: number;

  /** 遮挡查询超时（毫秒） */
  private occlusionQueryTimeout: number;

  /** 遮挡查询批处理大小 */
  private _occlusionQueryBatchSize: number;

  /** 当前帧计数 */
  private frameCount: number = 0;

  /** 遮挡剔除状态映射 */
  private occlusionStates: Map<Entity, OcclusionCullingState> = new Map();

  /** 遮挡查询映射 */
  private occlusionQueries: Map<Entity, any> = new Map();

  /** 遮挡查询时间映射 */
  private occlusionQueryTimes: Map<Entity, number> = new Map();

  /** 层次Z缓冲区 */
  private hierarchicalZBuffer: Float32Array | null = null;

  /** 层次Z缓冲区宽度 */
  private hzbWidth: number = 0;

  /** 层次Z缓冲区高度 */
  private hzbHeight: number = 0;

  /** 层次Z缓冲区级别数 */
  private _hzbLevels: number = 0;

  /** 遮挡渲染器 */
  private occlusionRenderer: THREE.WebGLRenderer | null = null;

  /** 遮挡相机 */
  private occlusionCamera: THREE.Camera | null = null;

  /** 遮挡场景 */
  private occlusionScene: THREE.Scene | null = null;

  /** 遮挡渲染目标 */
  private occlusionRenderTarget: THREE.WebGLRenderTarget | null = null;

  /** 遮挡查询着色器 */
  private occlusionQueryShader: THREE.ShaderMaterial | null = null;

  /** 遮挡查询几何体 */
  private occlusionQueryGeometry: THREE.BufferGeometry | null = null;

  /** 调试可视化材质 */
  private debugMaterial: THREE.MeshBasicMaterial | null = null;

  /** 调试可视化网格 */
  private debugMeshes: THREE.Mesh[] = [];

  /** 是否支持WebGL2 */
  private supportsWebGL2: boolean = false;

  /** 是否支持遮挡查询 */
  private supportsOcclusionQuery: boolean = false;

  /**
   * 创建遮挡剔除系统
   * @param options 遮挡剔除系统配置
   */
  constructor(options: OcclusionCullingSystemOptions = {}) {
    super();

    // 设置启用状态（使用基类方法）
    if (options.enabled !== undefined) {
      this.setEnabled(options.enabled);
    }
    this.algorithm = options.algorithm || OcclusionCullingAlgorithm.HIERARCHICAL_Z_BUFFER;
    this._precision = options.precision || 0.5;
    this.useHierarchicalZBuffer = options.useHierarchicalZBuffer !== undefined ? options.useHierarchicalZBuffer : true;
    this.useOcclusionQuery = options.useOcclusionQuery !== undefined ? options.useOcclusionQuery : true;
    this._useGPU = options.useGPU !== undefined ? options.useGPU : true;
    this._useConservativeOcclusion = options.useConservativeOcclusion !== undefined ? options.useConservativeOcclusion : true;
    this._useTemporalCoherence = options.useTemporalCoherence !== undefined ? options.useTemporalCoherence : true;
    this.useDebugVisualization = options.useDebugVisualization !== undefined ? options.useDebugVisualization : false;
    this.occlusionQueryInterval = options.occlusionQueryInterval || 5;
    this.occlusionQueryTimeout = options.occlusionQueryTimeout || 1000;
    this._occlusionQueryBatchSize = options.occlusionQueryBatchSize || 10;

    // 检查WebGL支持
    this.checkWebGLSupport();
  }

  /**
   * 检查WebGL支持
   */
  private checkWebGLSupport(): void {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl2');
      this.supportsWebGL2 = !!gl;
      this.supportsOcclusionQuery = this.supportsWebGL2;

      if (!this.supportsWebGL2 && this.algorithm === OcclusionCullingAlgorithm.HARDWARE_OCCLUSION_QUERY) {
        Debug.warn('OcclusionCullingSystem', 'WebGL2不支持，回退到层次Z缓冲区算法');
        this.algorithm = OcclusionCullingAlgorithm.HIERARCHICAL_Z_BUFFER;
      }
    } catch (error) {
      this.supportsWebGL2 = false;
      this.supportsOcclusionQuery = false;
      Debug.warn('OcclusionCullingSystem', 'WebGL2不支持，回退到层次Z缓冲区算法');
      this.algorithm = OcclusionCullingAlgorithm.HIERARCHICAL_Z_BUFFER;
    }
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    // 初始化遮挡剔除系统
    if (this.useHierarchicalZBuffer) {
      this.initializeHierarchicalZBuffer();
    }

    if (this.useOcclusionQuery && this.supportsOcclusionQuery) {
      this.initializeOcclusionQuery();
    }

    if (this.useDebugVisualization) {
      this.initializeDebugVisualization();
    }
  }

  /**
   * 初始化层次Z缓冲区
   */
  private initializeHierarchicalZBuffer(): void {
    // 创建层次Z缓冲区
    this.hzbWidth = 512;
    this.hzbHeight = 512;
    this._hzbLevels = Math.floor(Math.log2(Math.max(this.hzbWidth, this.hzbHeight))) + 1;
    this.hierarchicalZBuffer = new Float32Array(this.hzbWidth * this.hzbHeight);

    // 初始化层次Z缓冲区
    for (let i = 0; i < this.hierarchicalZBuffer.length; i++) {
      this.hierarchicalZBuffer[i] = 1.0; // 远平面
    }
  }

  /**
   * 初始化遮挡查询
   */
  private initializeOcclusionQuery(): void {
    // 创建遮挡渲染器
    this.occlusionRenderer = new THREE.WebGLRenderer({
      antialias: false,
      precision: 'lowp',
      powerPreference: 'high-performance'
    });
    this.occlusionRenderer.setSize(this.hzbWidth, this.hzbHeight);
    this.occlusionRenderer.setClearColor(0x000000, 0);

    // 创建遮挡相机
    this.occlusionCamera = new THREE.PerspectiveCamera(60, this.hzbWidth / this.hzbHeight, 0.1, 1000);

    // 创建遮挡场景
    this.occlusionScene = new THREE.Scene();

    // 创建遮挡渲染目标
    this.occlusionRenderTarget = new THREE.WebGLRenderTarget(this.hzbWidth, this.hzbHeight, {
      minFilter: THREE.NearestFilter,
      magFilter: THREE.NearestFilter,
      format: THREE.RGBAFormat,
      type: THREE.UnsignedByteType
    });

    // 创建遮挡查询着色器
    this.occlusionQueryShader = new THREE.ShaderMaterial({
      vertexShader: `
        void main() {
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        void main() {
          gl_FragColor = vec4(1.0, 0.0, 0.0, 1.0);
        }
      `,
      depthTest: true,
      depthWrite: false,
      colorWrite: false
    });

    // 创建遮挡查询几何体
    this.occlusionQueryGeometry = new THREE.BoxGeometry(1, 1, 1);
  }

  /**
   * 初始化调试可视化
   */
  private initializeDebugVisualization(): void {
    // 创建调试材质
    this.debugMaterial = new THREE.MeshBasicMaterial({
      wireframe: true,
      transparent: true,
      opacity: 0.5
    });
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.isEnabled()) {
      return;
    }

    // 增加帧计数
    this.frameCount++;

    // 获取相机和场景
    const camera = this.getCamera();
    const scene = this.getScene();

    if (!camera || !scene) {
      return;
    }

    // 更新遮挡相机
    if (this.occlusionCamera) {
      const threeCamera = camera.getThreeCamera();
      this.occlusionCamera.position.copy(threeCamera.position);
      this.occlusionCamera.rotation.copy(threeCamera.rotation);
      this.occlusionCamera.updateMatrixWorld();
    }

    // 根据算法执行遮挡剔除
    switch (this.algorithm) {
      case OcclusionCullingAlgorithm.HIERARCHICAL_Z_BUFFER:
        this.cullWithHierarchicalZBuffer(camera, scene);
        break;
      case OcclusionCullingAlgorithm.OCCLUSION_QUERY:
        this.cullWithOcclusionQuery(camera, scene);
        break;
      case OcclusionCullingAlgorithm.SOFTWARE_RASTERIZATION:
        this.cullWithSoftwareRasterization(camera, scene);
        break;
      case OcclusionCullingAlgorithm.HARDWARE_OCCLUSION_QUERY:
        this.cullWithHardwareOcclusionQuery(camera, scene);
        break;
    }

    // 更新调试可视化
    if (this.useDebugVisualization) {
      this.updateDebugVisualization();
    }
  }

  /**
   * 获取相机
   * @returns 相机
   */
  private getCamera(): Camera | null {
    if (!this.world) {
      return null;
    }

    // 遍历所有实体查找相机组件
    for (const entity of this.world.getEntities().values()) {
      const camera = entity.getComponent('Camera') as Camera;
      if (camera) {
        return camera;
      }
    }

    return null;
  }

  /**
   * 获取场景
   * @returns 场景
   */
  private getScene(): Scene | null {
    if (!this.world) {
      return null;
    }

    // 遍历所有实体查找场景组件
    for (const entity of this.world.getEntities().values()) {
      const scene = entity.getComponent('Scene');
      if (scene && scene instanceof Scene) {
        return scene;
      }
    }

    return null;
  }

  /**
   * 使用层次Z缓冲区进行遮挡剔除
   * @param camera 相机
   * @param scene 场景
   */
  public cullWithHierarchicalZBuffer(camera: Camera, scene: Scene): void {
    if (!this.world) {
      return;
    }

    // 获取可剔除组件
    const cullableComponents: CullableComponent[] = [];
    for (const entity of this.world.getEntities().values()) {
      const component = entity.getComponent('CullableComponent');
      if (component && component instanceof CullableComponent) {
        cullableComponents.push(component);
      }
    }

    if (cullableComponents.length === 0) {
      return;
    }

    // 更新层次Z缓冲区
    this.updateHierarchicalZBuffer(camera, scene);

    // 遍历可剔除组件
    for (const component of cullableComponents) {
      const entity = component.getEntity();
      if (!entity) {
        continue;
      }

      // 获取变换组件
      const transform = entity.getComponent('Transform') as Transform;
      if (!transform) {
        continue;
      }

      // 获取包围盒
      const boundingBox = component.getBoundingBox();
      if (!boundingBox) {
        continue;
      }

      // 检查是否被遮挡
      const isOccluded = this.isOccludedByHierarchicalZBuffer(boundingBox, camera, transform);

      // 更新可见性
      component.setVisible(!isOccluded);

      // 更新遮挡状态
      this.occlusionStates.set(entity, isOccluded ? OcclusionCullingState.INVISIBLE : OcclusionCullingState.VISIBLE);
    }
  }

  /**
   * 更新层次Z缓冲区
   * @param camera 相机
   * @param scene 场景
   */
  private updateHierarchicalZBuffer(camera: Camera, scene: Scene): void {
    if (!this.hierarchicalZBuffer) {
      return;
    }

    // 清除层次Z缓冲区
    for (let i = 0; i < this.hierarchicalZBuffer.length; i++) {
      this.hierarchicalZBuffer[i] = 1.0; // 远平面
    }

    // 获取潜在遮挡物
    const occluders = this.getPotentialOccluders(camera, scene);

    // 渲染潜在遮挡物到层次Z缓冲区
    for (const occluder of occluders) {
      this.renderOccluderToHierarchicalZBuffer(occluder, camera);
    }

    // 构建层次Z缓冲区的层次结构
    this.buildHierarchicalZBufferLevels();
  }

  /**
   * 获取潜在遮挡物
   * @param camera 相机
   * @param scene 场景
   * @returns 潜在遮挡物列表
   */
  private getPotentialOccluders(camera: Camera, scene: Scene): THREE.Object3D[] {
    const occluders: THREE.Object3D[] = [];

    // 遍历场景中的物体
    scene.getThreeScene().traverse((object: THREE.Object3D) => {
      // 检查是否是网格
      if (object instanceof THREE.Mesh) {
        // 检查是否是潜在遮挡物
        if (this.isPotentialOccluder(object, camera)) {
          occluders.push(object);
        }
      }
    });

    return occluders;
  }

  /**
   * 检查是否是潜在遮挡物
   * @param object 物体
   * @param camera 相机
   * @returns 是否是潜在遮挡物
   */
  private isPotentialOccluder(object: THREE.Object3D, camera: Camera): boolean {
    // 检查是否可见
    if (!object.visible) {
      return false;
    }

    // 检查是否有几何体
    if (!(object instanceof THREE.Mesh) || !object.geometry) {
      return false;
    }

    // 检查是否在相机视锥体内
    const threeCamera = camera.getThreeCamera();
    const cameraFrustum = new THREE.Frustum().setFromProjectionMatrix(
      new THREE.Matrix4().multiplyMatrices(
        threeCamera.projectionMatrix,
        threeCamera.matrixWorldInverse
      )
    );

    // 计算包围球
    object.geometry.computeBoundingSphere();
    const boundingSphere = object.geometry.boundingSphere!.clone();
    boundingSphere.applyMatrix4(object.matrixWorld);

    // 检查是否在视锥体内
    if (!cameraFrustum.intersectsSphere(boundingSphere)) {
      return false;
    }

    // 检查是否足够大
    const distance = threeCamera.position.distanceTo(boundingSphere.center);
    const apparentSize = boundingSphere.radius / distance;
    if (apparentSize < 0.01) {
      return false;
    }

    return true;
  }

  /**
   * 渲染遮挡物到层次Z缓冲区
   * @param _occluder 遮挡物
   * @param _camera 相机
   */
  private renderOccluderToHierarchicalZBuffer(_occluder: THREE.Object3D, _camera: Camera): void {
    // 在实际项目中，这里应该使用软件光栅化或WebGL渲染到深度缓冲区
    // 这里使用简化的实现
  }

  /**
   * 构建层次Z缓冲区的层次结构
   */
  private buildHierarchicalZBufferLevels(): void {
    // 在实际项目中，这里应该构建层次Z缓冲区的层次结构
    // 这里使用简化的实现
  }

  /**
   * 检查是否被层次Z缓冲区遮挡
   * @param _boundingBox 包围盒
   * @param _camera 相机
   * @param _transform 变换
   * @returns 是否被遮挡
   */
  private isOccludedByHierarchicalZBuffer(_boundingBox: THREE.Box3, _camera: Camera, _transform: Transform): boolean {
    // 在实际项目中，这里应该使用层次Z缓冲区检查是否被遮挡
    // 这里使用简化的实现
    return false;
  }

  /**
   * 使用遮挡查询进行遮挡剔除
   * @param camera 相机
   * @param scene 场景
   */
  public cullWithOcclusionQuery(camera: Camera, scene: Scene): void {
    if (!this.supportsOcclusionQuery || !this.occlusionRenderer || !this.occlusionScene || !this.occlusionCamera || !this.occlusionRenderTarget || !this.world) {
      return;
    }

    // 获取可剔除组件
    const cullableComponents: CullableComponent[] = [];
    for (const entity of this.world.getEntities().values()) {
      const component = entity.getComponent('CullableComponent');
      if (component && component instanceof CullableComponent) {
        cullableComponents.push(component);
      }
    }

    if (cullableComponents.length === 0) {
      return;
    }

    // 准备遮挡查询
    this.prepareOcclusionQuery(camera, scene);

    // 遍历可剔除组件
    for (const component of cullableComponents) {
      const entity = component.getEntity();
      if (!entity) {
        continue;
      }

      // 获取变换组件
      const transform = entity.getComponent('Transform') as Transform;
      if (!transform) {
        continue;
      }

      // 检查是否需要执行遮挡查询
      if (this.shouldPerformOcclusionQuery(entity)) {
        // 执行遮挡查询
        this.performOcclusionQuery(entity, component, transform, camera);
      }

      // 更新可见性
      const occlusionState = this.occlusionStates.get(entity) || OcclusionCullingState.VISIBLE;
      component.setVisible(occlusionState === OcclusionCullingState.VISIBLE);
    }

    // 处理遮挡查询结果
    this.processOcclusionQueryResults();
  }

  /**
   * 准备遮挡查询
   * @param camera 相机
   * @param scene 场景
   */
  private prepareOcclusionQuery(camera: Camera, scene: Scene): void {
    if (!this.occlusionRenderer || !this.occlusionScene || !this.occlusionCamera || !this.occlusionRenderTarget) {
      return;
    }

    // 清除遮挡场景
    while (this.occlusionScene.children.length > 0) {
      this.occlusionScene.remove(this.occlusionScene.children[0]);
    }

    // 添加潜在遮挡物到遮挡场景
    const occluders = this.getPotentialOccluders(camera, scene);
    for (const occluder of occluders) {
      this.occlusionScene.add(occluder.clone());
    }

    // 渲染遮挡场景到遮挡渲染目标
    this.occlusionRenderer.setRenderTarget(this.occlusionRenderTarget);
    this.occlusionRenderer.clear();
    this.occlusionRenderer.render(this.occlusionScene, this.occlusionCamera);
    this.occlusionRenderer.setRenderTarget(null);
  }

  /**
   * 检查是否应该执行遮挡查询
   * @param entity 实体
   * @returns 是否应该执行遮挡查询
   */
  private shouldPerformOcclusionQuery(entity: Entity): boolean {
    // 检查是否已经在查询中
    const occlusionState = this.occlusionStates.get(entity);
    if (occlusionState === OcclusionCullingState.QUERYING) {
      return false;
    }

    // 检查是否超过查询间隔
    if (this.frameCount % this.occlusionQueryInterval !== 0) {
      return false;
    }

    // 检查是否超过查询超时
    const queryTime = this.occlusionQueryTimes.get(entity);
    if (queryTime && performance.now() - queryTime < this.occlusionQueryTimeout) {
      return false;
    }

    return true;
  }

  /**
   * 执行遮挡查询
   * @param entity 实体
   * @param component 可剔除组件
   * @param transform 变换组件
   * @param _camera 相机
   */
  private performOcclusionQuery(entity: Entity, component: CullableComponent, transform: Transform, _camera: Camera): void {
    if (!this.occlusionRenderer || !this.occlusionScene || !this.occlusionCamera || !this.occlusionRenderTarget || !this.occlusionQueryGeometry || !this.occlusionQueryShader) {
      return;
    }

    // 获取包围盒
    const boundingBox = component.getBoundingBox();
    if (!boundingBox) {
      return;
    }

    // 创建查询网格
    const queryMesh = new THREE.Mesh(this.occlusionQueryGeometry, this.occlusionQueryShader);
    queryMesh.position.copy(transform.getPosition());
    queryMesh.rotation.copy(transform.getRotation());
    queryMesh.scale.copy(boundingBox.getSize(new THREE.Vector3()));
    queryMesh.updateMatrixWorld();

    // 添加到遮挡场景
    this.occlusionScene.add(queryMesh);

    // 创建遮挡查询
    const gl = this.occlusionRenderer.getContext() as WebGL2RenderingContext;
    const query = gl.createQuery();
    if (!query) {
      return;
    }

    // 开始遮挡查询
    gl.beginQuery(gl.ANY_SAMPLES_PASSED, query);

    // 渲染查询网格
    this.occlusionRenderer.setRenderTarget(this.occlusionRenderTarget);
    this.occlusionRenderer.render(this.occlusionScene, this.occlusionCamera);
    this.occlusionRenderer.setRenderTarget(null);

    // 结束遮挡查询
    gl.endQuery(gl.ANY_SAMPLES_PASSED);

    // 移除查询网格
    this.occlusionScene.remove(queryMesh);

    // 保存查询
    this.occlusionQueries.set(entity, query);
    this.occlusionQueryTimes.set(entity, performance.now());
    this.occlusionStates.set(entity, OcclusionCullingState.QUERYING);
  }

  /**
   * 处理遮挡查询结果
   */
  private processOcclusionQueryResults(): void {
    if (!this.occlusionRenderer) {
      return;
    }

    const gl = this.occlusionRenderer.getContext() as WebGL2RenderingContext;

    // 遍历所有查询
    for (const [entity, query] of this.occlusionQueries.entries()) {
      // 检查查询是否可用
      if (gl.getQueryParameter(query, gl.QUERY_RESULT_AVAILABLE)) {
        // 获取查询结果
        const result = gl.getQueryParameter(query, gl.QUERY_RESULT);

        // 更新遮挡状态
        this.occlusionStates.set(entity, result > 0 ? OcclusionCullingState.VISIBLE : OcclusionCullingState.INVISIBLE);

        // 删除查询
        gl.deleteQuery(query);
        this.occlusionQueries.delete(entity);
      }
    }
  }

  /**
   * 使用软件光栅化进行遮挡剔除
   * @param _camera 相机
   * @param _scene 场景
   */
  public cullWithSoftwareRasterization(_camera: Camera, _scene: Scene): void {
    // 在实际项目中，这里应该使用软件光栅化进行遮挡剔除
    // 这里使用简化的实现
  }

  /**
   * 使用硬件遮挡查询进行遮挡剔除
   * @param _camera 相机
   * @param _scene 场景
   */
  public cullWithHardwareOcclusionQuery(_camera: Camera, _scene: Scene): void {
    // 在实际项目中，这里应该使用硬件遮挡查询进行遮挡剔除
    // 这里使用简化的实现
  }

  /**
   * 更新调试可视化
   */
  private updateDebugVisualization(): void {
    // 清除调试网格
    for (const mesh of this.debugMeshes) {
      mesh.parent?.remove(mesh);
    }
    this.debugMeshes = [];

    // 获取场景
    const scene = this.getScene();
    if (!scene) {
      return;
    }

    // 遍历所有遮挡状态
    for (const [entity, state] of this.occlusionStates.entries()) {
      // 获取变换组件
      const transform = entity.getComponent('Transform') as Transform;
      if (!transform) {
        continue;
      }

      // 获取可剔除组件
      const component = entity.getComponent('CullableComponent') as CullableComponent;
      if (!component) {
        continue;
      }

      // 获取包围盒
      const boundingBox = component.getBoundingBox();
      if (!boundingBox) {
        continue;
      }

      // 创建调试网格
      const debugMesh = new THREE.Mesh(
        new THREE.BoxGeometry(
          boundingBox.max.x - boundingBox.min.x,
          boundingBox.max.y - boundingBox.min.y,
          boundingBox.max.z - boundingBox.min.z
        ),
        this.debugMaterial!.clone()
      );

      // 设置调试网格颜色
      switch (state) {
        case OcclusionCullingState.VISIBLE:
          (debugMesh.material as THREE.MeshBasicMaterial).color.set(0x00ff00); // 绿色
          break;
        case OcclusionCullingState.INVISIBLE:
          (debugMesh.material as THREE.MeshBasicMaterial).color.set(0xff0000); // 红色
          break;
        case OcclusionCullingState.QUERYING:
          (debugMesh.material as THREE.MeshBasicMaterial).color.set(0xffff00); // 黄色
          break;
        case OcclusionCullingState.UNKNOWN:
          (debugMesh.material as THREE.MeshBasicMaterial).color.set(0x0000ff); // 蓝色
          break;
      }

      // 设置调试网格位置
      debugMesh.position.copy(transform.getPosition());
      debugMesh.rotation.copy(transform.getRotation());

      // 添加到场景
      scene.getThreeScene().add(debugMesh);
      this.debugMeshes.push(debugMesh);
    }
  }

  /**
   * 获取遮挡剔除结果
   * @returns 遮挡剔除结果
   */
  public getOcclusionCullingResult(): OcclusionCullingResult {
    let visibleCount = 0;
    let invisibleCount = 0;
    let queryingCount = 0;
    let unknownCount = 0;

    // 统计各种状态的数量
    for (const state of this.occlusionStates.values()) {
      switch (state) {
        case OcclusionCullingState.VISIBLE:
          visibleCount++;
          break;
        case OcclusionCullingState.INVISIBLE:
          invisibleCount++;
          break;
        case OcclusionCullingState.QUERYING:
          queryingCount++;
          break;
        case OcclusionCullingState.UNKNOWN:
          unknownCount++;
          break;
      }
    }

    // 计算总数和剔除率
    const totalCount = visibleCount + invisibleCount + queryingCount + unknownCount;
    const cullingRate = totalCount > 0 ? invisibleCount / totalCount : 0;

    return {
      visibleCount,
      invisibleCount,
      queryingCount,
      unknownCount,
      totalCount,
      cullingRate,
      queryTime: 0 // 在实际项目中，这里应该记录查询时间
    };
  }



  /**
   * 设置算法
   * @param algorithm 算法
   */
  public setAlgorithm(algorithm: OcclusionCullingAlgorithm): void {
    this.algorithm = algorithm;
  }

  /**
   * 获取算法
   * @returns 算法
   */
  public getAlgorithm(): OcclusionCullingAlgorithm {
    return this.algorithm;
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 清除遮挡查询
    if (this.occlusionRenderer) {
      const gl = this.occlusionRenderer.getContext() as WebGL2RenderingContext;
      for (const query of this.occlusionQueries.values()) {
        gl.deleteQuery(query);
      }
      this.occlusionQueries.clear();
    }

    // 销毁渲染目标
    if (this.occlusionRenderTarget) {
      this.occlusionRenderTarget.dispose();
      this.occlusionRenderTarget = null;
    }

    // 销毁渲染器
    if (this.occlusionRenderer) {
      this.occlusionRenderer.dispose();
      this.occlusionRenderer = null;
    }

    // 清除调试网格
    for (const mesh of this.debugMeshes) {
      mesh.parent?.remove(mesh);
      mesh.geometry.dispose();
      (mesh.material as THREE.Material).dispose();
    }
    this.debugMeshes = [];

    // 清除调试材质
    if (this.debugMaterial) {
      this.debugMaterial.dispose();
      this.debugMaterial = null;
    }

    // 清除遮挡查询几何体
    if (this.occlusionQueryGeometry) {
      this.occlusionQueryGeometry.dispose();
      this.occlusionQueryGeometry = null;
    }

    // 清除遮挡查询着色器
    if (this.occlusionQueryShader) {
      this.occlusionQueryShader.dispose();
      this.occlusionQueryShader = null;
    }

    // 清除层次Z缓冲区
    this.hierarchicalZBuffer = null;

    // 清除状态映射
    this.occlusionStates.clear();
    this.occlusionQueryTimes.clear();

    // 调用父类的dispose方法
    super.dispose();
  }
}
