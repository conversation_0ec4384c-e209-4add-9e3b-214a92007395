/**
 * 增强的实例化渲染系统
 * 支持更多的实例属性和更高效的实例更新
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { Camera   } from '../Camera';
import { Scene } from '../../scene/Scene';
import type { Transform } from '../../scene/Transform';
import { InstancedComponent, InstanceData } from './InstancedComponent';
import { InstancedRenderingSystem, InstancedRenderingSystemOptions } from './InstancedRenderingSystem';
import { Debug } from '../../utils/Debug';

/**
 * 增强的实例数据接口
 */
export interface EnhancedInstanceData extends InstanceData {
  /** 自定义属性 */
  customAttributes?: Map<string, number | number[] | THREE.Vector2 | THREE.Vector3 | THREE.Vector4 | THREE.Color>;
  /** 动画状态 */
  animationState?: {
    clipName: string;
    time: number;
    weight: number;
  };
  /** 物理属性 */
  physicsProperties?: {
    velocity: THREE.Vector3;
    angularVelocity: THREE.Vector3;
    mass: number;
  };
  /** 是否需要更新 */
  needsUpdate?: boolean;
  /** 是否可见 */
  visible?: boolean;
  /** 是否投射阴影 */
  castShadow?: boolean;
  /** 是否接收阴影 */
  receiveShadow?: boolean;
  /** 渲染顺序 */
  renderOrder?: number;
  /** 层级 */
  layer?: number;
  /** 用户数据 */
  userData?: any;
}

/**
 * 增强的实例化渲染系统配置接口
 */
export interface EnhancedInstancedRenderingSystemOptions extends InstancedRenderingSystemOptions {
  /** 是否使用GPU实例更新 */
  useGPUInstanceUpdate?: boolean;
  /** 是否使用实例批处理 */
  useInstanceBatching?: boolean;
  /** 是否使用实例合并 */
  useInstanceMerging?: boolean;
  /** 是否使用实例LOD */
  useInstanceLOD?: boolean;
  /** 是否使用实例剔除 */
  useInstanceCulling?: boolean;
  /** 是否使用实例缓存 */
  useInstanceCache?: boolean;
  /** 是否使用实例排序 */
  useInstanceSorting?: boolean;
  /** 是否使用实例分组 */
  useInstanceGrouping?: boolean;
  /** 是否使用实例动画 */
  useInstanceAnimation?: boolean;
  /** 是否使用实例物理 */
  useInstancePhysics?: boolean;
  /** 是否使用实例阴影 */
  useInstanceShadow?: boolean;
  /** 是否使用实例材质变体 */
  useInstanceMaterialVariants?: boolean;
  /** 最大批处理大小 */
  maxBatchSize?: number;
  /** 最大实例数量 */
  maxInstanceCount?: number;
  /** 更新间隔（帧） */
  updateInterval?: number;
  /** 是否使用调试可视化 */
  useDebugVisualization?: boolean;
}

/**
 * 实例批处理组接口
 */
export interface InstanceBatchGroup {
  /** 批处理组ID */
  id: string;
  /** 几何体 */
  geometry: THREE.BufferGeometry;
  /** 材质 */
  material: THREE.Material;
  /** 实例化网格 */
  instancedMesh: THREE.InstancedMesh;
  /** 实例数据列表 */
  instances: EnhancedInstanceData[];
  /** 实例到索引的映射 */
  instanceToIndex: Map<string, number>;
  /** 可用索引列表 */
  availableIndices: number[];
  /** 是否需要更新 */
  needsUpdate: boolean;
  /** 是否可见 */
  visible: boolean;
  /** 是否投射阴影 */
  castShadow: boolean;
  /** 是否接收阴影 */
  receiveShadow: boolean;
  /** 渲染顺序 */
  renderOrder: number;
  /** 层级 */
  layer: number;
  /** 用户数据 */
  userData: any;
  /** 自定义属性缓冲区 */
  customAttributeBuffers: Map<string, THREE.InstancedBufferAttribute>;
}

/**
 * 增强的实例化渲染系统类
 */
export class EnhancedInstancedRenderingSystem extends InstancedRenderingSystem {
  /** 系统类型 */
  private static readonly TYPE: string = 'EnhancedInstancedRenderingSystem';

  /** 是否使用GPU实例更新 */
  private useGPUInstanceUpdate: boolean;

  /** 是否使用实例批处理 */
  private useInstanceBatching: boolean;

  /** 是否使用实例合并 */
  private useInstanceMerging: boolean;

  /** 是否使用实例LOD */
  private useInstanceLOD: boolean;

  /** 是否使用实例剔除 */
  private useInstanceCulling: boolean;

  /** 是否使用实例缓存 */
  private useInstanceCache: boolean;

  /** 是否使用实例排序 */
  private useInstanceSorting: boolean;

  /** 是否使用实例分组 */
  private useInstanceGrouping: boolean;

  /** 是否使用实例动画 */
  private useInstanceAnimation: boolean;

  /** 是否使用实例物理 */
  private useInstancePhysics: boolean;

  /** 是否使用实例阴影 */
  private useInstanceShadow: boolean;

  /** 是否使用实例材质变体 */
  private useInstanceMaterialVariants: boolean;

  /** 最大批处理大小 */
  private maxBatchSize: number;

  /** 最大实例数量 */
  private maxInstanceCount: number;

  /** 更新间隔（帧） */
  private updateInterval: number;

  /** 是否使用调试可视化 */
  private useDebugVisualization: boolean;

  /** 当前帧计数 */
  private frameCount: number = 0;

  /** 实例批处理组列表 */
  private batchGroups: Map<string, InstanceBatchGroup> = new Map();

  /** 几何体到批处理组的映射 */
  private geometryToBatchGroup: Map<string, InstanceBatchGroup> = new Map();

  /** 材质到批处理组的映射 */
  private materialToBatchGroup: Map<string, InstanceBatchGroup> = new Map();

  /** 实例到批处理组的映射 */
  private instanceToBatchGroup: Map<string, InstanceBatchGroup> = new Map();

  /** 实例到实例数据的映射 */
  private instanceToData: Map<string, EnhancedInstanceData> = new Map();

  /** 实例计数器 */
  private instanceCounter: number = 0;

  /** 批处理组计数器 */
  private batchGroupCounter: number = 0;

  /** 自定义属性定义列表 */
  private customAttributeDefinitions: Map<string, { size: number, type: THREE.AttributeType }> = new Map();

  /** 实例更新器 */
  private instanceUpdater: GPUInstanceUpdater | null = null;

  /** 调试可视化材质 */
  private debugMaterial: THREE.MeshBasicMaterial | null = null;

  /** 调试可视化网格 */
  private debugMeshes: THREE.Mesh[] = [];

  /**
   * 创建增强的实例化渲染系统
   * @param options 增强的实例化渲染系统配置
   */
  constructor(options: EnhancedInstancedRenderingSystemOptions = {}) {
    super(options);

    this.useGPUInstanceUpdate = options.useGPUInstanceUpdate !== undefined ? options.useGPUInstanceUpdate : true;
    this.useInstanceBatching = options.useInstanceBatching !== undefined ? options.useInstanceBatching : true;
    this.useInstanceMerging = options.useInstanceMerging !== undefined ? options.useInstanceMerging : false;
    this.useInstanceLOD = options.useInstanceLOD !== undefined ? options.useInstanceLOD : false;
    this.useInstanceCulling = options.useInstanceCulling !== undefined ? options.useInstanceCulling : true;
    this.useInstanceCache = options.useInstanceCache !== undefined ? options.useInstanceCache : true;
    this.useInstanceSorting = options.useInstanceSorting !== undefined ? options.useInstanceSorting : false;
    this.useInstanceGrouping = options.useInstanceGrouping !== undefined ? options.useInstanceGrouping : false;
    this.useInstanceAnimation = options.useInstanceAnimation !== undefined ? options.useInstanceAnimation : false;
    this.useInstancePhysics = options.useInstancePhysics !== undefined ? options.useInstancePhysics : false;
    this.useInstanceShadow = options.useInstanceShadow !== undefined ? options.useInstanceShadow : true;
    this.useInstanceMaterialVariants = options.useInstanceMaterialVariants !== undefined ? options.useInstanceMaterialVariants : false;
    this.maxBatchSize = options.maxBatchSize || 1000;
    this.maxInstanceCount = options.maxInstanceCount || 10000;
    this.updateInterval = options.updateInterval || 1;
    this.useDebugVisualization = options.useDebugVisualization !== undefined ? options.useDebugVisualization : false;

    // 初始化自定义属性定义
    this.initializeCustomAttributeDefinitions();

    // 初始化GPU实例更新器
    if (this.useGPUInstanceUpdate) {
      this.instanceUpdater = new GPUInstanceUpdater();
    }
  }

  /**
   * 初始化自定义属性定义
   */
  private initializeCustomAttributeDefinitions(): void {
    // 添加常用自定义属性定义
    this.customAttributeDefinitions.set('color', { size: 3, type: THREE.FloatType });
    this.customAttributeDefinitions.set('emissive', { size: 3, type: THREE.FloatType });
    this.customAttributeDefinitions.set('roughness', { size: 1, type: THREE.FloatType });
    this.customAttributeDefinitions.set('metalness', { size: 1, type: THREE.FloatType });
    this.customAttributeDefinitions.set('opacity', { size: 1, type: THREE.FloatType });
    this.customAttributeDefinitions.set('scale', { size: 3, type: THREE.FloatType });
    this.customAttributeDefinitions.set('rotation', { size: 4, type: THREE.FloatType });
    this.customAttributeDefinitions.set('velocity', { size: 3, type: THREE.FloatType });
    this.customAttributeDefinitions.set('angularVelocity', { size: 3, type: THREE.FloatType });
    this.customAttributeDefinitions.set('animationTime', { size: 1, type: THREE.FloatType });
    this.customAttributeDefinitions.set('animationWeight', { size: 1, type: THREE.FloatType });
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    super.initialize();

    // 初始化调试可视化
    if (this.useDebugVisualization) {
      this.initializeDebugVisualization();
    }
  }

  /**
   * 初始化调试可视化
   */
  private initializeDebugVisualization(): void {
    // 创建调试材质
    this.debugMaterial = new THREE.MeshBasicMaterial({
      wireframe: true,
      transparent: true,
      opacity: 0.5
    });
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 增加帧计数
    this.frameCount++;

    // 检查是否需要更新
    if (this.frameCount % this.updateInterval !== 0) {
      return;
    }

    // 获取相机和场景
    const camera = this.getCamera();
    const scene = this.getScene();

    if (!camera || !scene) {
      return;
    }

    // 更新批处理组
    this.updateBatchGroups(camera);

    // 更新调试可视化
    if (this.useDebugVisualization) {
      this.updateDebugVisualization();
    }
  }

  /**
   * 获取相机
   * @returns 相机
   */
  private getCamera(): Camera | null {
    // 获取相机组件
    const cameras = this.entityManager.getComponentsOfType<Camera>('Camera');
    if (cameras.length === 0) {
      return null;
    }

    // 返回第一个相机
    return cameras[0];
  }

  /**
   * 获取场景
   * @returns 场景
   */
  private getScene(): Scene | null {
    // 获取场景组件
    const scenes = this.entityManager.getComponentsOfType<Scene>('Scene');
    if (scenes.length === 0) {
      return null;
    }

    // 返回第一个场景
    return scenes[0];
  }

  /**
   * 更新批处理组
   * @param camera 相机
   */
  private updateBatchGroups(camera: Camera): void {
    // 遍历所有批处理组
    for (const batchGroup of this.batchGroups.values()) {
      // 如果批处理组不需要更新，则跳过
      if (!batchGroup.needsUpdate) {
        continue;
      }

      // 更新批处理组
      this.updateBatchGroup(batchGroup, camera);

      // 重置更新标志
      batchGroup.needsUpdate = false;
    }
  }

  /**
   * 更新批处理组
   * @param batchGroup 批处理组
   * @param camera 相机
   */
  private updateBatchGroup(batchGroup: InstanceBatchGroup, camera: Camera): void {
    // 获取实例化网格
    const instancedMesh = batchGroup.instancedMesh;

    // 更新实例化网格属性
    instancedMesh.visible = batchGroup.visible;
    instancedMesh.castShadow = batchGroup.castShadow;
    instancedMesh.receiveShadow = batchGroup.receiveShadow;
    instancedMesh.renderOrder = batchGroup.renderOrder;
    instancedMesh.layers.set(batchGroup.layer);

    // 如果使用GPU实例更新，则使用GPU更新实例
    if (this.useGPUInstanceUpdate && this.instanceUpdater) {
      this.instanceUpdater.updateWithGPU(instancedMesh, batchGroup.instances);
    } else {
      // 否则使用CPU更新实例
      this.updateInstancesWithCPU(batchGroup, camera);
    }

    // 更新自定义属性
    this.updateCustomAttributes(batchGroup);
  }

  /**
   * 使用CPU更新实例
   * @param batchGroup 批处理组
   * @param camera 相机
   */
  private updateInstancesWithCPU(batchGroup: InstanceBatchGroup, camera: Camera): void {
    // 获取实例化网格
    const instancedMesh = batchGroup.instancedMesh;

    // 创建临时矩阵
    const matrix = new THREE.Matrix4();

    // 遍历所有实例
    for (let i = 0; i < batchGroup.instances.length; i++) {
      // 获取实例数据
      const instance = batchGroup.instances[i];

      // 如果实例不可见，则跳过
      if (instance.visible === false) {
        continue;
      }

      // 如果使用实例剔除，则检查实例是否在视锥体内
      if (this.useInstanceCulling && !this.isInstanceInFrustum(instance, camera)) {
        continue;
      }

      // 计算实例矩阵
      matrix.compose(
        instance.position,
        instance.rotation,
        instance.scale
      );

      // 设置实例矩阵
      instancedMesh.setMatrixAt(i, matrix);
    }

    // 标记实例矩阵需要更新
    instancedMesh.instanceMatrix.needsUpdate = true;
  }

  /**
   * 检查实例是否在视锥体内
   * @param instance 实例数据
   * @param camera 相机
   * @returns 是否在视锥体内
   */
  private isInstanceInFrustum(instance: EnhancedInstanceData, camera: Camera): boolean {
    // 创建视锥体
    const frustum = new THREE.Frustum().setFromProjectionMatrix(
      new THREE.Matrix4().multiplyMatrices(
        camera.getThreeCamera().projectionMatrix,
        camera.getThreeCamera().matrixWorldInverse
      )
    );

    // 创建包围球
    const boundingSphere = new THREE.Sphere(instance.position, instance.scale.length());

    // 检查包围球是否在视锥体内
    return frustum.intersectsSphere(boundingSphere);
  }

  /**
   * 更新自定义属性
   * @param batchGroup 批处理组
   */
  private updateCustomAttributes(batchGroup: InstanceBatchGroup): void {
    // 遍历所有自定义属性
    for (const [name, buffer] of batchGroup.customAttributeBuffers.entries()) {
      // 获取属性定义
      const definition = this.customAttributeDefinitions.get(name);
      if (!definition) {
        continue;
      }

      // 遍历所有实例
      for (let i = 0; i < batchGroup.instances.length; i++) {
        // 获取实例数据
        const instance = batchGroup.instances[i];

        // 如果实例没有自定义属性，则跳过
        if (!instance.customAttributes) {
          continue;
        }

        // 获取自定义属性值
        const value = instance.customAttributes.get(name);
        if (value === undefined) {
          continue;
        }

        // 设置自定义属性值
        if (typeof value === 'number') {
          buffer.setX(i, value);
        } else if (Array.isArray(value)) {
          for (let j = 0; j < Math.min(value.length, definition.size); j++) {
            buffer.setComponent(i, j, value[j]);
          }
        } else if (value instanceof THREE.Vector2) {
          buffer.setXY(i, value.x, value.y);
        } else if (value instanceof THREE.Vector3) {
          buffer.setXYZ(i, value.x, value.y, value.z);
        } else if (value instanceof THREE.Vector4) {
          buffer.setXYZW(i, value.x, value.y, value.z, value.w);
        } else if (value instanceof THREE.Color) {
          buffer.setXYZ(i, value.r, value.g, value.b);
        }
      }

      // 标记自定义属性需要更新
      buffer.needsUpdate = true;
    }
  }

  /**
   * 创建批处理组
   * @param geometry 几何体
   * @param material 材质
   * @param maxInstances 最大实例数
   * @returns 批处理组
   */
  public createBatchGroup(geometry: THREE.BufferGeometry, material: THREE.Material, maxInstances: number = this.maxBatchSize): InstanceBatchGroup {
    // 生成批处理组ID
    const batchGroupId = `batch_${this.batchGroupCounter++}`;

    // 创建实例化网格
    const instancedMesh = new THREE.InstancedMesh(geometry, material, maxInstances);
    instancedMesh.name = `instanced_mesh_${batchGroupId}`;
    instancedMesh.frustumCulled = false; // 禁用自动视锥体剔除，由系统自己处理
    instancedMesh.castShadow = this.useInstanceShadow;
    instancedMesh.receiveShadow = this.useInstanceShadow;

    // 创建批处理组
    const batchGroup: InstanceBatchGroup = {
      id: batchGroupId,
      geometry,
      material,
      instancedMesh,
      instances: [],
      instanceToIndex: new Map(),
      availableIndices: Array.from({ length: maxInstances }, (_, i) => i),
      needsUpdate: true,
      visible: true,
      castShadow: this.useInstanceShadow,
      receiveShadow: this.useInstanceShadow,
      renderOrder: 0,
      layer: 0,
      userData: {},
      customAttributeBuffers: new Map()
    };

    // 创建自定义属性缓冲区
    for (const [name, definition] of this.customAttributeDefinitions.entries()) {
      const buffer = new THREE.InstancedBufferAttribute(
        new Float32Array(maxInstances * definition.size),
        definition.size
      );
      batchGroup.customAttributeBuffers.set(name, buffer);
      geometry.setAttribute(name, buffer);
    }

    // 添加到批处理组列表
    this.batchGroups.set(batchGroupId, batchGroup);

    // 添加到场景
    const scene = this.getScene();
    if (scene) {
      scene.getObject3D().add(instancedMesh);
    }

    return batchGroup;
  }

  /**
   * 获取或创建批处理组
   * @param geometry 几何体
   * @param material 材质
   * @returns 批处理组
   */
  public getOrCreateBatchGroup(geometry: THREE.BufferGeometry, material: THREE.Material): InstanceBatchGroup {
    // 生成几何体和材质的键
    const geometryId = geometry.uuid;
    const materialId = material.uuid;
    const key = `${geometryId}_${materialId}`;

    // 检查是否已经存在批处理组
    let batchGroup = this.geometryToBatchGroup.get(key);
    if (batchGroup) {
      return batchGroup;
    }

    // 创建新的批处理组
    batchGroup = this.createBatchGroup(geometry, material);

    // 添加到映射
    this.geometryToBatchGroup.set(key, batchGroup);
    this.materialToBatchGroup.set(materialId, batchGroup);

    return batchGroup;
  }

  /**
   * 添加实例
   * @param geometry 几何体
   * @param material 材质
   * @param instanceData 实例数据
   * @returns 实例ID
   */
  public addInstance(geometry: THREE.BufferGeometry, material: THREE.Material, instanceData: EnhancedInstanceData): string {
    // 生成实例ID
    const instanceId = `instance_${this.instanceCounter++}`;

    // 获取或创建批处理组
    const batchGroup = this.getOrCreateBatchGroup(geometry, material);

    // 如果批处理组已满，则创建新的批处理组
    if (batchGroup.availableIndices.length === 0) {
      const newBatchGroup = this.createBatchGroup(geometry, material);
      this.geometryToBatchGroup.set(`${geometry.uuid}_${material.uuid}`, newBatchGroup);
      return this.addInstance(geometry, material, instanceData);
    }

    // 获取可用索引
    const index = batchGroup.availableIndices.pop()!;

    // 设置实例ID
    instanceData.id = instanceId;

    // 设置默认值
    instanceData.position = instanceData.position || new THREE.Vector3();
    instanceData.rotation = instanceData.rotation || new THREE.Quaternion();
    instanceData.scale = instanceData.scale || new THREE.Vector3(1, 1, 1);
    instanceData.visible = instanceData.visible !== undefined ? instanceData.visible : true;
    instanceData.needsUpdate = true;

    // 添加实例到批处理组
    batchGroup.instances[index] = instanceData;
    batchGroup.instanceToIndex.set(instanceId, index);
    batchGroup.needsUpdate = true;

    // 添加到映射
    this.instanceToBatchGroup.set(instanceId, batchGroup);
    this.instanceToData.set(instanceId, instanceData);

    return instanceId;
  }

  /**
   * 更新实例
   * @param instanceId 实例ID
   * @param instanceData 实例数据
   */
  public updateInstance(instanceId: string, instanceData: Partial<EnhancedInstanceData>): void {
    // 获取批处理组
    const batchGroup = this.instanceToBatchGroup.get(instanceId);
    if (!batchGroup) {
      return;
    }

    // 获取实例索引
    const index = batchGroup.instanceToIndex.get(instanceId);
    if (index === undefined) {
      return;
    }

    // 获取原始实例数据
    const originalData = batchGroup.instances[index];
    if (!originalData) {
      return;
    }

    // 更新实例数据
    if (instanceData.position) {
      originalData.position.copy(instanceData.position);
    }
    if (instanceData.rotation) {
      originalData.rotation.copy(instanceData.rotation);
    }
    if (instanceData.scale) {
      originalData.scale.copy(instanceData.scale);
    }
    if (instanceData.visible !== undefined) {
      originalData.visible = instanceData.visible;
    }
    if (instanceData.customAttributes) {
      originalData.customAttributes = originalData.customAttributes || new Map();
      for (const [name, value] of instanceData.customAttributes.entries()) {
        originalData.customAttributes.set(name, value);
      }
    }
    if (instanceData.animationState) {
      originalData.animationState = { ...originalData.animationState, ...instanceData.animationState };
    }
    if (instanceData.physicsProperties) {
      originalData.physicsProperties = { ...originalData.physicsProperties, ...instanceData.physicsProperties };
    }

    // 标记实例需要更新
    originalData.needsUpdate = true;
    batchGroup.needsUpdate = true;
  }

  /**
   * 移除实例
   * @param instanceId 实例ID
   */
  public removeInstance(instanceId: string): void {
    // 获取批处理组
    const batchGroup = this.instanceToBatchGroup.get(instanceId);
    if (!batchGroup) {
      return;
    }

    // 获取实例索引
    const index = batchGroup.instanceToIndex.get(instanceId);
    if (index === undefined) {
      return;
    }

    // 移除实例
    batchGroup.instances[index] = null!;
    batchGroup.instanceToIndex.delete(instanceId);
    batchGroup.availableIndices.push(index);
    batchGroup.needsUpdate = true;

    // 移除映射
    this.instanceToBatchGroup.delete(instanceId);
    this.instanceToData.delete(instanceId);
  }

  /**
   * 创建自定义实例属性
   * @param name 属性名
   * @param size 属性大小
   * @param type 属性类型
   */
  public createInstanceAttribute(name: string, size: number, type: THREE.AttributeType): void {
    // 添加自定义属性定义
    this.customAttributeDefinitions.set(name, { size, type });

    // 为所有批处理组添加自定义属性
    for (const batchGroup of this.batchGroups.values()) {
      const buffer = new THREE.InstancedBufferAttribute(
        new Float32Array(batchGroup.instancedMesh.count * size),
        size
      );
      batchGroup.customAttributeBuffers.set(name, buffer);
      batchGroup.geometry.setAttribute(name, buffer);
      batchGroup.needsUpdate = true;
    }
  }

  /**
   * 更新调试可视化
   */
  private updateDebugVisualization(): void {
    // 清除调试网格
    for (const mesh of this.debugMeshes) {
      mesh.parent?.remove(mesh);
    }
    this.debugMeshes = [];

    // 获取场景
    const scene = this.getScene();
    if (!scene) {
      return;
    }

    // 可视化批处理组
    for (const batchGroup of this.batchGroups.values()) {
      // 创建批处理组调试网格
      const batchMesh = new THREE.Mesh(
        new THREE.BoxBufferGeometry(1, 1, 1),
        this.debugMaterial!.clone()
      );

      // 设置批处理组调试网格颜色
      (batchMesh.material as THREE.MeshBasicMaterial).color.set(0x00ff00); // 绿色

      // 设置批处理组调试网格位置
      batchMesh.position.set(0, 0, 0);
      batchMesh.scale.set(0.5, 0.5, 0.5);

      // 添加到场景
      scene.getObject3D().add(batchMesh);
      this.debugMeshes.push(batchMesh);
    }
  }

  /**
   * 销毁系统
   */
  public destroy(): void {
    super.destroy();

    // 清除批处理组
    for (const batchGroup of this.batchGroups.values()) {
      // 移除实例化网格
      batchGroup.instancedMesh.parent?.remove(batchGroup.instancedMesh);
      (batchGroup.instancedMesh as any).dispose();
    }

    // 清除映射
    this.batchGroups.clear();
    this.geometryToBatchGroup.clear();
    this.materialToBatchGroup.clear();
    this.instanceToBatchGroup.clear();
    this.instanceToData.clear();

    // 清除调试网格
    for (const mesh of this.debugMeshes) {
      mesh.parent?.remove(mesh);
      (mesh.geometry as any).dispose();
      (mesh.material as THREE.Material).dispose();
    }
    this.debugMeshes = [];

    // 清除调试材质
    if (this.debugMaterial) {
      (this.debugMaterial as any).dispose();
      this.debugMaterial = null;
    }
  }
}
